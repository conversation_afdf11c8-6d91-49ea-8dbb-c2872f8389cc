﻿namespace Webaby.Core.DatabaseDMV.Queries
{
    public class MostExpensiveStatementLogData
    {
        public Guid Id { get; set; }

        // Use byte[] for cross-database binary compatibility
        public byte[] QueryHash { get; set; }

        public long ExecutionCount { get; set; }

        public long CpuPerExecution { get; set; }

        public long TotalCPU { get; set; }

        public long IOPerExecution { get; set; }

        public long TotalIO { get; set; }

        public long AverageElapsedTime { get; set; }

        public long AverageTimeBlocked { get; set; }

        public long AverageRowsReturned { get; set; }

        public string QueryText { get; set; }

        public string ParentQuery { get; set; }

        public string ExecutionPlan { get; set; }

        public DateTime CreationTime { get; set; }

        public DateTime LastExecutionTime { get; set; }

        public DateTime SQLServerStartTime { get; set; }

        public int HoursOnline { get; set; }

        public DateTime DateInitiallyChecked { get; set; }

        public DateTime DateLastChecked { get; set; }

        public int RowNumber { get; set; }
    }
}