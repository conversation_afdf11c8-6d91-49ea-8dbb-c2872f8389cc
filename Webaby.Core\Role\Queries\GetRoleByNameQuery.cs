﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Security.Role.Queries;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByNameQuery : QueryBase<AspNetRoleData>
    {
        public string RoleName { get; set; }
    }

    internal class GetRoleByNameQueryHandler : QueryHandlerBase<GetRoleByNameQuery, AspNetRoleData>
    {
        public GetRoleByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<AspNetRoleData>> ExecuteAsync(GetRoleByNameQuery query)
        {
            // Bước 1: L<PERSON><PERSON> danh sách role theo tên từ EntitySet
            IQueryable<AspNetRoleEntity> roleQuery = EntitySet.Get<AspNetRoleEntity>()
                .Where(role => role.Name == query.RoleName);

            // Bước 2: <PERSON><PERSON><PERSON> ngh<PERSON>a hàm ánh xạ từ entity sang data
            Func<AspNetRoleEntity, AspNetRoleData> mapToData = entity => Mapper.Map<AspNetRoleData>(entity);

            // Bước 3: Tạo kết quả phân trang và trả về
            QueryResult<AspNetRoleData> result = QueryResult.Create(
                roleQuery,
                query.Pagination,
                mapToData
            );
            return result;
        }
    }
}