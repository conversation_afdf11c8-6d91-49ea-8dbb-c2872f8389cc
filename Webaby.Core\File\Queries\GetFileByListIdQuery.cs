﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.File.Queries
{
    public class GetFileByListIdQuery : QueryBase<FileData>
    {
        public List<Guid> IdList { get; set; }
    }

    internal class GetFileByListIdQueryHandler : QueryHandlerBase<GetFileByListIdQuery, FileData>
    {
        public GetFileByListIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetFileByListIdQuery query)
        {
            var entities = await EntitySet.Get<FileEntity>()
                .Where(file => query.IdList.Contains(file.Id))
                .ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<FileData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
