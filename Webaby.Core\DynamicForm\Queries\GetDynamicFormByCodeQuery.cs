﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFormByCodeQuery : QueryBase<DynamicFormData>
    {
        public string Code { get; set; }
    }

    internal class GetDynamicFormByCodeQueryHandler : QueryHandlerBase<GetDynamicFormByCodeQuery, DynamicFormData>
    {
        public GetDynamicFormByCodeQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<DynamicFormData>> ExecuteAsync(GetDynamicFormByCodeQuery query)
        {
            var entities = await EntitySet.Get<DynamicFormEntity>()
                .Where(df => df.Code == query.Code)
                .ToListAsync();

            var mapped = entities.Select(x => Mapper.Map<DynamicFormData>(x));
            return QueryResult.Create(mapped);
        }
    }
}