﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.Organization.Queries
{
    public class GetOrganizationQuery : QueryBase<OrganizationData>
    {
        public Guid? Id { get; set; }

    }

    internal class GetOrganizationQueryHandler : QueryHandlerBase<GetOrganizationQuery, OrganizationData>
    {
        public GetOrganizationQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<OrganizationData>> ExecuteAsync(GetOrganizationQuery query)
        {
            var entities = await EntitySet.Get<OrganizationEntity>().ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<OrganizationData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
