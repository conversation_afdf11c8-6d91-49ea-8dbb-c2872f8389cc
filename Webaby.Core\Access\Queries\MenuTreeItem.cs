﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Webaby.Web
{
    public class GetMenuTreeItemQuery : QueryBase<MenuTreeItem>
    {
    }
    public interface ITreeNode
    {
        Guid Id { get; set; }
        Guid? ParentId { get; set; }
    }

    public class MenuTreeItem
    {
        public ITreeNode NodeData { get; set; }
        public List<MenuTreeItem> Childrens { get; set; }

        public static List<MenuTreeItem> ParseFromList(List<ITreeNode> rawData, Guid? parentId)
        {
            var root = rawData.Where(x => x.ParentId == parentId)
                .Select(x => new MenuTreeItem
                {
                    NodeData = x,
                    Childrens = ParseFromList(rawData, x.Id)
                }).ToList();
            return root;
        }

        public int Depth()
        {
            if (Childrens.Count == 0)
            {
                return 1;
            }
            var max = 1;
            foreach (var c in Childrens)
            {
                var cd = c.Depth();
                if (cd > max) max = cd;
            }
            return max + 1;
        }
    }
}
