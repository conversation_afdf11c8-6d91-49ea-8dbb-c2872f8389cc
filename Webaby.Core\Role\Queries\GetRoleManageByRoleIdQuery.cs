﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Security.Role.Queries;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleManageByRoleIdQuery : QueryBase<AspNetRoleData>
    {
        public Guid? ManageId { get; private set; }

        public GetRoleManageByRoleIdQuery(Guid manageId)
        {
            ManageId = manageId;
        }
    }

    internal class GetRoleManageByRoleIdQueryHandler : QueryHandlerBase<GetRoleManageByRoleIdQuery, AspNetRoleData>
    {
        public GetRoleManageByRoleIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<AspNetRoleData>> ExecuteAsync(GetRoleManageByRoleIdQuery query)
        {
            var entities = await (from r in EntitySet.Get<AspNetRoleEntity>()
                                  join rm in EntitySet.Get<RoleManageEntity>() on r.Id equals rm.RoleId
                                  where rm.RoleManageId == query.ManageId.Value
                                  select r).ToListAsync();

            var mapped = entities.Select(x => Mapper.Map<AspNetRoleData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
