﻿using System;
using System.Linq;

namespace Webaby.Core.File.Queries
{
    public class GetFileInfoByIdQuery : QueryBase<FileData>
    {
        public Guid Id { get; set; }
    }

    internal class GetFileInfoByIdQueryHandler : QueryHandlerBase<GetFileInfoByIdQuery, FileData>
    {
        public override QueryResult<FileData> Execute(GetFileInfoByIdQuery query)
        {
            var fileData = from file in EntitySet.Get<FileEntity>()
                           where file.Id == query.Id
                           select new FileData
                           {
                               Id = file.Id,
                               Descriptions = file.Descriptions,
                               Extensions = file.Extension,
                               FileName = file.FileName,
                               ReferenceFileId = file.ReferenceFileId,
                               ReferenceObjectId = file.ReferenceObjectId,
                               ReferenceObjectType = file.ReferenceObjectType,
                           };
            return new QueryResult<FileData>(fileData);
        }
    }
}
