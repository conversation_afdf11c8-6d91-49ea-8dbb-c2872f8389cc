﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.Access
{
    [Table(Name = "dbo.AccessBusinessPermission")]
    public class AccessBusinessPermissionEntity : Entity,ICreatedDateEnabledEntity,ICreatedByEnabledEntity,IModifiedByEnabledEntity,IModifiedDateEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Column(IsPrimaryKey = true)]
        public override Guid Id { get; set; }

        [Column]
        public Guid AccessId { get; set; }

        [Column]
        public Guid BusinessPermissionId { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }

        [Column]
        public Boolean Deleted { get; set; }
    }
}
