﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.Access
{
    [Table(Name = "dbo.RoleBusinessPermission")]
    public class RoleBusinessPermissionEntity : Entity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity, IModifiedDateEnabledEntity, IModifiedByEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Column(IsPrimaryKey = true)]
        public override Guid Id { get; set; }

        [Column]
        public Guid RoleId { get; set; }

        [Column]
        public Guid BusinessPermissionId { get; set; }

        [Column]
        public Boolean? OrganizationBased { get; set; }

        [Column]
        public Boolean? CreatorBased { get; set; }

        [Column]
        public Boolean? OwnerBased { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }

        [Column]
        public bool Deleted { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }
    }
}
