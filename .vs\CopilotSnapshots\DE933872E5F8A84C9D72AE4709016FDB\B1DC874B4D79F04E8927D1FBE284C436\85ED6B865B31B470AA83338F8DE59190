﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.File.Queries
{
    public class GetFileInfoByIdQuery : QueryBase<FileData>
    {
        public Guid Id { get; set; }
    }

    internal class GetFileInfoByIdQueryHandler : QueryHandlerBase<GetFileInfoByIdQuery, FileData>
    {
        public GetFileInfoByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetFileInfoByIdQuery query)
        {
            var entities = await EntitySet.Get<FileEntity>()
                .Where(file => file.Id == query.Id)
                .ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<FileData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
