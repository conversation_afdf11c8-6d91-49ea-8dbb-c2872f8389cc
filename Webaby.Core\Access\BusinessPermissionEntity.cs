﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.Access
{
    [Table(Name = "dbo.BusinessPermission")]
    public class BusinessPermissionEntity : Entity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity, IModifiedDateEnabledEntity, IModifiedByEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Column(IsPrimaryKey = true)]
        public override Guid Id { get; set; }

        [Column]
        public string Name { get; set; }

        [Column]
        public Guid? ParentId { get; set; }

        [Column]
        public int Order { get; set; }

        [Column]
        public Boolean DisplayOnly { get; set; }

        [Column]
        public Boolean OrganizationBasedEnabled { get; set; }

        [Column]
        public Boolean CreatorBasedEnabled { get; set; }

        [Column]
        public Boolean OwnerBasedEnabled { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }

        [Column]
        public Boolean Deleted { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }
    }
}
