﻿using System.Linq;
using Webaby.Core.File;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFormQuery : QueryBase<DynamicFormListItem>
    {
        public string Name { get; set; }
    }

    internal class GetDynamicFormQueryHandler : QueryHandlerBase<GetDynamicFormQuery, DynamicFormListItem>
    {
        public GetDynamicFormQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<DynamicFormListItem>> ExecuteAsync(GetDynamicFormQuery query)
        {
            var mainQuery = from form in EntitySet.Get<DynamicFormEntity>()
                            join file in EntitySet.Get<FileEntity>() on form.DisplayDocumentFileId equals file.Id into fileN
                            from fileX in fileN.DefaultIfEmpty()
                            select new DynamicFormListItem
                            {
                                Id = form.Id,
                                Name = form.Name,
                                Formula = form.Formula,
                                DisplayDocumentFileName = fileX.FileName,
                                FormOverwrite = form.FormOverwrite,
                                TurnOffWarning = form.TurnOffWarning,
                                DisplayDocumentFileId = form.DisplayDocumentFileId
                            };

            if (!string.IsNullOrWhiteSpace(query.Name))
            {
                mainQuery = mainQuery.Where(x => x.Name.Contains(query.Name));
            }

            var entities = await mainQuery.ToListAsync();
            return QueryResult.Create(entities, query.Pagination);
        }
    }
}
