﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.File.Queries
{
    public class GetFileInfoListByReferenceObjectIdQuery : QueryBase<FileData>
    {
        public Guid ReferenceObjectId { get; set; }
    }

    internal class GetFileInfoListByReferenceObjectIdQueryHandler : QueryHandlerBase<GetFileInfoListByReferenceObjectIdQuery, FileData>
    {
        public GetFileInfoListByReferenceObjectIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetFileInfoListByReferenceObjectIdQuery query)
        {
            var entities = await EntitySet.Get<FileEntity>()
                .Where(file => file.ReferenceObjectId == query.ReferenceObjectId)
                .ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<FileData>(x));
            return QueryResult.Create(mapped);
        }
    }
}