﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Security.Role.Queries;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByNameQuery : QueryBase<AspNetRoleData>
    {
        public string RoleName { get; set; }
    }

    internal class GetRoleByNameQueryHandler : QueryHandlerBase<GetRoleByNameQuery, AspNetRoleData>
    {
        public GetRoleByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<AspNetRoleData>> ExecuteAsync(GetRoleByNameQuery query)
        {
            var entities = await EntitySet.Get<AspNetRoleEntity>()
                .Where(x => x.Name == query.RoleName)
                .ToListAsync();

            var mapped = entities.Select(x => Mapper.Map<AspNetRoleData>(x));
            return QueryResult.Create(mapped, query.Pagination);
        }
    }
}