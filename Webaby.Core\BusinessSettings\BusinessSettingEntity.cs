﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.BusinessSettings
{
    [Table(Name = "dbo.BusinessSettings")]
    public class BusinessSettingEntity : Entity
    {
        [Column(IsPrimaryKey = true)]
        public override Guid Id { get; set; }

        [Column]
        public string Name { get; set; }

        [Column]
        public string DisplayName { get; set; }

        [Column]
        public string ImportKey { get; set; }

        [Column]
        public string Value { get; set; }

        [Column]
        public string DataType { get; set; }

        [Column]
        public string Description { get; set; }

        [Column]
        public int Order { get; set; }

        [Column]
        public bool IsReadOnly { get; set; }
    }
}
