﻿using System;
using System.Linq;
using System.Collections.Generic;

namespace Webaby.Core.File.Queries
{
    public class GetFileByListIdQuery : QueryBase<FileData>
    {
        public List<Guid> IdList { get; set; }
    }

    internal class GetFileByListIdQueryHandler : QueryHandlerBase<GetFileByListIdQuery, FileData>
    {
        public override QueryResult<FileData> Execute(GetFileByListIdQuery query)
        {
            var fileData = from file in EntitySet.Get<FileEntity>()
                           where query.IdList.Contains(file.Id)
                           select new FileData
                           {
                               Id = file.Id,
                               Descriptions = file.Descriptions,
                               Extensions = file.Extension,
                               FileName = file.FileName,
                               ReferenceFileId = file.ReferenceFileId,
                               ReferenceObjectId = file.ReferenceObjectId,
                               ReferenceObjectType = file.ReferenceObjectType,
                           };
            return new QueryResult<FileData>(fileData);
        }
    }
}
