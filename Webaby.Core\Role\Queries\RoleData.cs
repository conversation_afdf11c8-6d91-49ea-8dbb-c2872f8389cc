﻿using System;
using AutoMapper;

namespace Webaby.Core.Role.Queries
{
    public class RoleData
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public Int64 Permissions { get; set; }

        public string LicenseKey { get; set; }

        public int TotalCount { get; set; }

        public static RoleData FromEntity(RoleEntity entity)
        {
            return Mapper.Map<RoleEntity, RoleData>(entity);
        }
    }
}
